import { db } from "../config/firebase.js";

// Audit log types
export const AuditLogTypes = {
  AUTH: "auth",
  USER: "user",
  ADMIN: "admin",
  SYSTEM: "system",
  EMAIL: "email",
  AUDIT: "audit"
};

// Audit log actions
export const AuditLogActions = {
  // Auth actions
  LOGIN_SUCCESS: "login_success",
  LOGIN_FAILED: "login_failed",
  LOGOUT: "logout",
  PASSWORD_RESET: "password_reset",
  PASSWORD_CHANGED: "password_changed",
  ACCESS_GRANTED: "access_granted",
  ACCESS_DENIED: "access_denied",
  AUTHENTICATION_FAILED: "authentication_failed",
  TOKEN_GENERATED: "token_generated",
  TOKEN_GENERATION_FAILED: "token_generation_failed",
  TOKEN_VALIDATED: "token_validated",
  TOKEN_VALIDATION_FAILED: "token_validation_failed",

  // User management actions
  USER_CREATED: "user_created",
  USER_UPDATED: "user_updated",
  USER_DELETED: "user_deleted",
  USER_ACTIVATED: "user_activated",
  USER_DEACTIVATED: "user_deactivated",
  USER_PERMANENTLY_DELETED: "user_permanently_deleted",
  USER_CREATION_FAILED: "user_creation_failed",
  USER_UPDATE_FAILED: "user_update_failed",
  USER_DELETION_FAILED: "user_deletion_failed",
  USERS_VIEWED: "users_viewed",
  USER_PROFILE_VIEWED: "user_profile_viewed",
  USER_PROFILE_ACCESSED: "user_profile_accessed",

  // Admin actions
  SETTINGS_UPDATED: "settings_updated",
  SYSTEM_CONFIGURED: "system_configured",
  PASSWORD_RESET_LINK_GENERATED: "password_reset_link_generated",
  PASSWORD_RESET_LINK_FAILED: "password_reset_link_failed",

  // Email actions
  EMAIL_SENT: "email_sent",
  EMAIL_FAILED: "email_failed",
  EMAILS_FETCHED: "emails_fetched",
  EMAIL_DETAIL_VIEWED: "email_detail_viewed",
  EMAIL_ACCESS_DENIED: "email_access_denied",
  EMAIL_LABELS_ACCESSED: "email_labels_accessed",
  ADMIN_EMAIL_ACCESS: "admin_email_access",
  
  // Audit actions
  VIEWED_LOGS: "viewed_logs",
  ACKNOWLEDGED_ALERT: "acknowledged_alert",
  EXPORTED_LOGS: "exported_logs",
  AUDIT_METADATA_ACCESSED: "audit_metadata_accessed",
  AUDIT_STATS_ACCESSED: "audit_stats_accessed",
  SECURITY_ALERTS_ACCESSED: "security_alerts_accessed",

  // System actions
  SYSTEM_HEALTH_CHECK: "system_health_check",
  SYSTEM_STATUS_CHECK: "system_status_check",
  API_ENDPOINT_ACCESSED: "api_endpoint_accessed",
  UNAUTHORIZED_ACCESS_ATTEMPT: "unauthorized_access_attempt"
};

// Log severity levels
export const LogSeverity = {
  INFO: "info",
  WARNING: "warning",
  ERROR: "error",
  CRITICAL: "critical"
};

/**
 * Create an audit log entry
 * @param {Object} logData - The log data
 * @param {string} logData.type - The log type (from AuditLogTypes)
 * @param {string} logData.action - The action (from AuditLogActions)
 * @param {string} logData.performedBy - The user ID who performed the action
 * @param {Object} logData.details - Additional details about the action
 * @param {string} [logData.severity=LogSeverity.INFO] - The severity level
 * @param {string} [logData.targetUser] - The user ID who was the target of the action
 * @returns {Promise<string>} - The ID of the created log entry
 */
export const createAuditLog = async (logData) => {
  try {
    const {
      type,
      action,
      performedBy,
      details,
      severity = LogSeverity.INFO,
      targetUser = null
    } = logData;
    
    // Create log entry
    const logEntry = {
      type,
      action,
      performedBy,
      details,
      severity,
      timestamp: new Date(),
      targetUser
    };
    
    // Add to Firestore
    const docRef = await db.collection("auditLogs").add(logEntry);
    console.log(`Audit log created: ${docRef.id}`);
    
    return docRef.id;
  } catch (error) {
    console.error("Error creating audit log:", error);
    // Don't throw - we don't want audit logging to break the application
    return null;
  }
};

// Safe version that doesn't throw errors
export const safeCreateAuditLog = async (logData) => {
  try {
    await createAuditLog(logData);
    return true;
  } catch (error) {
    console.error("Error in safeCreateAuditLog:", error);
    return false;
  }
};

/**
 * Get recent audit logs for a specific user
 * @param {string} userId - The user ID to get logs for
 * @param {number} limit - Maximum number of logs to return
 * @returns {Array} Array of audit logs
 */
export const getUserAuditLogs = async (userId, limit = 10) => {
  try {
    const snapshot = await db.collection("auditLogs")
      .where("performedBy", "==", userId)
      .orderBy("timestamp", "desc")
      .limit(limit)
      .get();
    
    const logs = [];
    snapshot.forEach(doc => {
      logs.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    return logs;
  } catch (error) {
    console.error("Error getting user audit logs:", error);
    return [];
  }
};

/**
 * Get security alerts that need attention
 * @param {number} limit - Maximum number of alerts to return
 * @returns {Array} Array of security alerts
 */
export const getSecurityAlerts = async (limit = 50) => {
  try {
    const snapshot = await db.collection("securityAlerts")
      .where("acknowledged", "==", false)
      .orderBy("timestamp", "desc")
      .limit(limit)
      .get();
    
    const alerts = [];
    snapshot.forEach(doc => {
      alerts.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    return alerts;
  } catch (error) {
    console.error("Error getting security alerts:", error);
    return [];
  }
};

/**
 * Acknowledge a security alert
 * @param {string} alertId - The ID of the alert to acknowledge
 * @param {string} acknowledgedBy - User ID who acknowledged the alert
 * @returns {boolean} Success status
 */
export const acknowledgeSecurityAlert = async (alertId, acknowledgedBy) => {
  try {
    await db.collection("securityAlerts").doc(alertId).update({
      acknowledged: true,
      acknowledgedBy,
      acknowledgedAt: new Date().toISOString()
    });
    return true;
  } catch (error) {
    console.error("Error acknowledging security alert:", error);
    return false;
  }
};


