"use client"

import { useState, useEffect, useCallback } from "react"
import { ChevronDown, Search, RefreshCw, AlertTriangle, Filter, X } from "lucide-react"
import {
  getAuditLogs,
  getAuditLogMetadata,
  formatTimestamp,
  getSeverityClass,
  getActionClass
} from "../../services/auditService"
import "./AuditLogs.css"

const AuditLogs = () => {
  // State management
  const [auditLogs, setAuditLogs] = useState([])
  const [metadata, setMetadata] = useState({ types: [], actions: [], severities: [] })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    totalPages: 1,
    hasMore: false
  })

  // Filter state
  const [filters, setFilters] = useState({
    type: "",
    action: "",
    performedBy: "",
    severity: "",
    search: ""
  })

  // UI state
  const [showFilters, setShowFilters] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  // Load audit logs
  const loadAuditLogs = useCallback(async (resetPagination = false) => {
    try {
      setLoading(true)
      setError(null)

      const currentPage = resetPagination ? 1 : pagination.page
      const params = {
        page: currentPage,
        limit: pagination.limit,
        sortBy: 'timestamp',
        sortOrder: 'desc', // Latest first
        ...filters
      }

      // Remove empty filters
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key]
        }
      })

      const response = await getAuditLogs(params)

      // Sort logs by timestamp descending (latest first) as backup
      const sortedLogs = (response.logs || []).sort((a, b) => {
        const dateA = new Date(a.timestamp)
        const dateB = new Date(b.timestamp)
        return dateB - dateA // Descending order
      })

      setAuditLogs(sortedLogs)
      setPagination({
        page: currentPage,
        limit: pagination.limit,
        totalPages: response.totalPages || 1,
        hasMore: response.hasMore || false
      })
    } catch (err) {
      console.error('Error loading audit logs:', err)
      setError(err.message || 'Failed to load audit logs')
    } finally {
      setLoading(false)
    }
  }, [filters, pagination.page, pagination.limit])

  // Load metadata for filters
  const loadMetadata = async () => {
    try {
      const metadataResponse = await getAuditLogMetadata()
      setMetadata(metadataResponse)
    } catch (err) {
      console.error('Error loading metadata:', err)
    }
  }

  // Handle filter changes
  const handleFilterChange = (name, value) => {
    setFilters(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      type: "",
      action: "",
      performedBy: "",
      severity: "",
      search: ""
    })
  }

  // Refresh data
  const handleRefresh = async () => {
    setRefreshing(true)
    await Promise.all([loadAuditLogs(true), loadMetadata()])
    setRefreshing(false)
  }

  // Load data on component mount
  useEffect(() => {
    loadMetadata()
    loadAuditLogs(true)
  }, [loadAuditLogs])

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadAuditLogs(true)
    }, 500) // 500ms debounce for search

    return () => clearTimeout(timeoutId)
  }, [filters.search, loadAuditLogs])

  // Filter changes effect (excluding search)
  useEffect(() => {
    loadAuditLogs(true)
  }, [filters.type, filters.action, filters.performedBy, filters.severity, loadAuditLogs])

  // Handle pagination
  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }))
    loadAuditLogs()
  }

  return (
    <div className="audit-logs-container">
      <div className="audit-logs-header">
        <div className="header-actions">
          <button
            className="refresh-button"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw size={16} className={refreshing ? 'spinning' : ''} />
            Refresh
          </button>
          <button
            className="filter-toggle-button"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter size={16} />
            Filters
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="search-container">
        <div className="search-input-wrapper">
          <Search size={16} className="search-icon" />
          <input
            type="text"
            placeholder="Search audit logs (action, user name, email, type, severity, details)..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className="search-input"
          />
          {filters.search && (
            <button
              onClick={() => handleFilterChange('search', '')}
              className="clear-search-button"
              title="Clear search"
            >
              <X size={14} />
            </button>
          )}
        </div>
        {filters.search && (
          <div className="search-indicator">
            <span>Searching for: "{filters.search}"</span>
          </div>
        )}
      </div>

      {showFilters && (
        <div className="filters-container">
          <div className="filters-grid">
            <div className="filter-group">
              <label>Type</label>
              <select
                value={filters.type}
                onChange={(e) => handleFilterChange('type', e.target.value)}
                className="filter-select"
              >
                <option value="">All Types</option>
                {metadata.types.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="filter-group">
              <label>Action</label>
              <select
                value={filters.action}
                onChange={(e) => handleFilterChange('action', e.target.value)}
                className="filter-select"
              >
                <option value="">All Actions</option>
                {metadata.actions.map(action => (
                  <option key={action.value} value={action.value}>
                    {action.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="filter-group">
              <label>Severity</label>
              <select
                value={filters.severity}
                onChange={(e) => handleFilterChange('severity', e.target.value)}
                className="filter-select"
              >
                <option value="">All Severities</option>
                {metadata.severities.map(severity => (
                  <option key={severity.value} value={severity.value}>
                    {severity.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="filter-group">
              <label>User ID</label>
              <input
                type="text"
                placeholder="Filter by user ID..."
                value={filters.performedBy}
                onChange={(e) => handleFilterChange('performedBy', e.target.value)}
                className="filter-input"
              />
            </div>
          </div>

          <div className="filter-actions">
            <button onClick={clearFilters} className="clear-filters-button">
              <X size={16} />
              Clear Filters
            </button>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="error-container">
          <AlertTriangle size={20} />
          <span>Error: {error}</span>
          <button onClick={() => loadAuditLogs(true)} className="retry-button">
            Retry
          </button>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="loading-container">
          <RefreshCw size={20} className="spinning" />
          <span>Loading audit logs...</span>
        </div>
      )}

      {/* Audit Logs Table */}
      {!loading && !error && (
        <div className="logs-table-container">
          <div className="table-header">
            <div className="results-info">
              <span className="results-count">
                Showing {auditLogs.length} results
                {pagination.totalPages > 1 && (
                  <span className="pagination-info-header">
                    (Page {pagination.page} of {pagination.totalPages})
                  </span>
                )}
              </span>
              {(filters.search || filters.type || filters.action || filters.severity || filters.performedBy) && (
                <span className="filters-active">
                  Filters active
                </span>
              )}
            </div>
            <div className="table-actions">
              <span className="sort-indicator">
                Latest first ↓
              </span>
            </div>
          </div>

          <div className="table-wrapper">
            <table className="audit-table">
              <thead>
                <tr>
                  <th>Timestamp</th>
                  <th>User</th>
                  <th>Type</th>
                  <th>Action</th>
                  <th>Severity</th>
                  <th>Details</th>
                </tr>
              </thead>
              <tbody>
                {auditLogs.length === 0 ? (
                  <tr>
                    <td colSpan="6" className="no-data">
                      No audit logs found
                    </td>
                  </tr>
                ) : (
                  auditLogs.map((log) => (
                    <tr key={log.id} className="audit-row">
                      <td className="timestamp-cell">
                        {formatTimestamp(log.timestamp)}
                      </td>
                      <td className="user-cell">
                        <div className="user-info">
                          <div className="user-avatar">
                            {log.performedByUser?.name?.charAt(0)?.toUpperCase() ||
                             log.performedBy?.charAt(0)?.toUpperCase() || 'U'}
                          </div>
                          <div className="user-details">
                            <span className="user-name">
                              {log.performedByUser?.name || log.performedBy || 'Unknown'}
                            </span>
                            <span className="user-id-small">
                              {log.performedByUser?.id || log.performedBy}
                            </span>
                            {log.performedByUser?.email && (
                              <span className="user-email">
                                {log.performedByUser.email}
                              </span>
                            )}
                            {log.targetUserInfo && (
                              <div className="target-user-info">
                                <span className="target-arrow">→</span>
                                <span className="target-user-name">
                                  {log.targetUserInfo.name}
                                </span>
                                <span className="target-user-id">
                                  {log.targetUserInfo.id}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="type-cell">
                        <span className="type-badge">{log.type}</span>
                      </td>
                      <td className="action-cell">
                        <span className={`action-badge ${getActionClass(log.action)}`}>
                          {log.action?.replace(/_/g, ' ')}
                        </span>
                      </td>
                      <td className="severity-cell">
                        <span className={`severity-badge ${getSeverityClass(log.severity)}`}>
                          {log.severity}
                        </span>
                      </td>
                      <td className="details-cell">
                        <div className="details-preview">
                          {log.details?.ipAddress && (
                            <span className="detail-item">IP: {log.details.ipAddress}</span>
                          )}
                          {log.details?.error && (
                            <span className="detail-item error">Error: {log.details.error}</span>
                          )}
                          {Object.keys(log.details || {}).length > 0 && (
                            <button
                              className="details-button"
                              onClick={() => console.log('Show details:', log.details)}
                            >
                              View Details
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="pagination-container">
              <button
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1}
                className="pagination-button"
              >
                Previous
              </button>

              <span className="pagination-info">
                Page {pagination.page} of {pagination.totalPages}
              </span>

              <button
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={!pagination.hasMore}
                className="pagination-button"
              >
                Next
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}



export default AuditLogs
