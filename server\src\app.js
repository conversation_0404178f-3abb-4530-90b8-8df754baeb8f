import express from 'express';
import cookieParser from 'cookie-parser';
import dotenv from 'dotenv';
import cors from 'cors';
import authRoutes from './routes/authRoutes.js';
import emailRoutes from './routes/emailRoutes.js';
import adminAuthRoutes from "./routes/adminAuthRoutes.js";
import adminRoutes from "./routes/adminRoutes.js";
import auditLogRoutes from "./routes/auditlogRoutes.js";
import userRoutes from "./routes/userRoutes.js";
import jwt from 'jsonwebtoken';
import path from 'path';
import { fileURLToPath } from 'url';
import { createAuditLog, AuditLogTypes, AuditLogActions, LogSeverity } from './utils/auditLogger.js';

// Load environment variables
dotenv.config();

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

// Allow multiple origins
const allowedOrigins = process.env.ALLOWED_ORIGINS 
  ? process.env.ALLOWED_ORIGINS.split(',') 
  : ['http://localhost:5173', 'http://localhost:5174'];

const corsOptions = {
  origin: function (origin, callback) {
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: 'GET,POST,PUT,DELETE',
  credentials: true,
};

// Middleware
app.use(cors(corsOptions));
app.use(express.json());
app.use(cookieParser());

// Add this middleware before your routes to debug token issues
app.use((req, res, next) => {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.split(' ')[1];
    console.log('Received token:', token.substring(0, 15) + '...');
    
    // Try to decode without verification to see what's in the token
    try {
      const decoded = jwt.decode(token);
      console.log('Token payload:', JSON.stringify(decoded));
    } catch (error) {
      console.error('Error decoding token:', error);
    }
  }
  
  next();
});

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, '../public')));

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/email', emailRoutes);
app.use("/api/admin/auth", adminAuthRoutes);
app.use("/api/admin", adminRoutes);
app.use('/api/user', userRoutes);
app.use("/api/auditlogs", auditLogRoutes);

// Add this after registering all routes
app.use((req, res, next) => {
  console.log(`Request received: ${req.method} ${req.path}`);
  next();
});

// Root endpoint
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    const healthData = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version
    };

    // Create audit log for health check
    await createAuditLog({
      type: AuditLogTypes.SYSTEM,
      action: AuditLogActions.SYSTEM_HEALTH_CHECK,
      performedBy: "system",
      details: {
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.headers['user-agent'],
        uptime: healthData.uptime,
        memoryUsage: healthData.memory
      },
      severity: LogSeverity.INFO
    });

    res.status(200).json(healthData);
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({ status: 'error', timestamp: new Date().toISOString() });
  }
});

// Add a catch-all route to handle 404s
app.use(async (req, res) => {
  console.log(`Route not found: ${req.method} ${req.path}`);

  // Create audit log for 404 errors
  await createAuditLog({
    type: AuditLogTypes.SYSTEM,
    action: AuditLogActions.UNAUTHORIZED_ACCESS_ATTEMPT,
    performedBy: "unknown",
    details: {
      method: req.method,
      path: req.path,
      query: req.query,
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.headers['user-agent']
    },
    severity: LogSeverity.WARNING
  });

  res.status(404).json({ error: "Not Found", path: req.path });
});

// Error handling middleware
app.use(async (err, req, res, next) => {
  console.error(err.stack);

  // Create audit log for server errors
  await createAuditLog({
    type: AuditLogTypes.SYSTEM,
    action: AuditLogActions.SYSTEM_STATUS_CHECK,
    performedBy: "system",
    details: {
      error: err.message,
      stack: err.stack,
      method: req.method,
      path: req.path,
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.headers['user-agent']
    },
    severity: LogSeverity.ERROR
  });

  res.status(500).json({
    error: 'Something went wrong!',
    message: err.message,
    stack: process.env.NODE_ENV === 'production' ? undefined : err.stack
  });
});

export default app;
