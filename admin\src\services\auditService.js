const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:5000';

/**
 * Get authentication headers with admin token
 */
const getAuthHeaders = () => {
  const token = localStorage.getItem('adminToken');
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  };
};

/**
 * Handle API response and errors
 */
const handleResponse = async (response) => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Network error' }));
    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
  }
  return response.json();
};

/**
 * Fetch audit logs with filtering and pagination
 * @param {Object} params - Query parameters
 * @param {number} params.page - Page number (default: 1)
 * @param {number} params.limit - Items per page (default: 20)
 * @param {string} params.type - Filter by log type
 * @param {string} params.action - Filter by action
 * @param {string} params.performedBy - Filter by user ID
 * @param {string} params.severity - Filter by severity level
 * @returns {Promise<Object>} Audit logs response
 */
export const getAuditLogs = async (params = {}) => {
  try {
    const queryParams = new URLSearchParams();
    
    // Add parameters to query string
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value);
      }
    });

    const response = await fetch(`${API_BASE_URL}/api/auditlogs?${queryParams}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    return await handleResponse(response);
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    throw error;
  }
};

/**
 * Get audit log metadata (types, actions, severities for filtering)
 * @returns {Promise<Object>} Metadata for filters
 */
export const getAuditLogMetadata = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/auditlogs/metadata`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    return await handleResponse(response);
  } catch (error) {
    console.error('Error fetching audit log metadata:', error);
    throw error;
  }
};

/**
 * Get audit log statistics
 * @param {number} days - Number of days to analyze (default: 30)
 * @returns {Promise<Object>} Statistics data
 */
export const getAuditLogStats = async (days = 30) => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/auditlogs/stats?days=${days}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    return await handleResponse(response);
  } catch (error) {
    console.error('Error fetching audit log statistics:', error);
    throw error;
  }
};

/**
 * Get security alerts (high severity logs)
 * @returns {Promise<Object>} Security alerts
 */
export const getSecurityAlerts = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/auditlogs/security-alerts`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    return await handleResponse(response);
  } catch (error) {
    console.error('Error fetching security alerts:', error);
    throw error;
  }
};

/**
 * Acknowledge a security alert
 * @param {string} alertId - ID of the alert to acknowledge
 * @param {string} notes - Optional notes about the acknowledgment
 * @returns {Promise<Object>} Acknowledgment response
 */
export const acknowledgeSecurityAlert = async (alertId, notes = '') => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/auditlogs/security-alerts/${alertId}/acknowledge`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({ notes })
    });

    return await handleResponse(response);
  } catch (error) {
    console.error('Error acknowledging security alert:', error);
    throw error;
  }
};

/**
 * Format timestamp for display
 * @param {string|Date} timestamp - Timestamp to format
 * @returns {string} Formatted timestamp
 */
export const formatTimestamp = (timestamp) => {
  if (!timestamp) return 'N/A';
  
  const date = new Date(timestamp);
  if (isNaN(date.getTime())) return 'Invalid Date';
  
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true
  });
};

/**
 * Get severity badge class for styling
 * @param {string} severity - Severity level
 * @returns {string} CSS class name
 */
export const getSeverityClass = (severity) => {
  switch (severity?.toLowerCase()) {
    case 'critical':
      return 'severity-critical';
    case 'error':
      return 'severity-error';
    case 'warning':
      return 'severity-warning';
    case 'info':
    default:
      return 'severity-info';
  }
};

/**
 * Get action badge class for styling
 * @param {string} action - Action type
 * @returns {string} CSS class name
 */
export const getActionClass = (action) => {
  if (!action) return '';
  
  const actionLower = action.toLowerCase();
  
  if (actionLower.includes('created') || actionLower.includes('signup')) return 'action-created';
  if (actionLower.includes('updated') || actionLower.includes('modified')) return 'action-updated';
  if (actionLower.includes('deleted') || actionLower.includes('removed')) return 'action-deleted';
  if (actionLower.includes('login') || actionLower.includes('authenticated')) return 'action-login';
  if (actionLower.includes('logout') || actionLower.includes('signout')) return 'action-logout';
  if (actionLower.includes('failed') || actionLower.includes('error')) return 'action-error';
  if (actionLower.includes('viewed') || actionLower.includes('accessed')) return 'action-viewed';
  
  return 'action-default';
};

export default {
  getAuditLogs,
  getAuditLogMetadata,
  getAuditLogStats,
  getSecurityAlerts,
  acknowledgeSecurityAlert,
  formatTimestamp,
  getSeverityClass,
  getActionClass
};
