.audit-logs-container {
  padding: 20px;
  background-color: #f8fafc;
  min-height: 100vh;
}

.audit-logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.audit-logs-header h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.refresh-button,
.alert-button,
.filter-toggle-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.filter-toggle-button:hover,
.refresh-button:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.refresh-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.refresh-button {
  background-color: #f0f0f0;
  color: #333;
}

.refresh-button:hover {
  background-color: #d0d0d0;
}

.alert-button {
  background-color: #ffcc00;
  color: #333;
}

.alert-button:hover {
  background-color: #e0b400;
}

.search-container {
  margin-bottom: 1rem;
}

.search-input-wrapper {
  position: relative;
  max-width: 400px;
}

.filters-container {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.search-input {
  width: 100%;
  padding: 10px 10px 10px 36px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.filter-button:hover {
  background-color: #f5f5f5;
}

.advanced-filters {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.filter-group {
  flex: 1;
}

.filter-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.filter-input, .filter-select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

.apply-filter-btn {
  padding: 8px 16px;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.apply-filter-btn:hover {
  background-color: #3a5ce5;
}

.reset-filter-btn {
  padding: 8px 16px;
  background-color: white;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.reset-filter-btn:hover {
  background-color: #f5f5f5;
}

.logs-table-container {
  overflow-x: auto;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.user-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f3f4f6;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.action-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.created {
  background-color: #d1fae5;
  color: #065f46;
}

.updated {
  background-color: #e0f2fe;
  color: #0369a1;
}

.deleted {
  background-color: #fee2e2;
  color: #b91c1c;
}

.login {
  background-color: #e0e7ff;
  color: #4338ca;
}

.failed {
  background-color: #fef3c7;
  color: #92400e;
}

.details-button {
  padding: 4px 8px;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.details-button:hover {
  background-color: #e5e7eb;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #666;
}

.error-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #b91c1c;
  background-color: #fee2e2;
  border-radius: 8px;
  padding: 16px;
}

.no-results {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 16px;
}

/* New styles for enhanced audit logs */
.clear-filters-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-filters-button:hover {
  background: #e5e7eb;
}

.error-container,
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  margin: 1rem 0;
}

.error-container {
  color: #dc2626;
  background: #fef2f2;
  border: 1px solid #fecaca;
}

.retry-button {
  margin-left: 1rem;
  padding: 0.25rem 0.75rem;
  background: #dc2626;
  color: white;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.results-count {
  color: #6b7280;
  font-size: 0.875rem;
}

.table-wrapper {
  overflow-x: auto;
}

.audit-table {
  width: 100%;
  border-collapse: collapse;
}

.audit-table th,
.audit-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.audit-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.audit-row:hover {
  background: #f9fafb;
}

.timestamp-cell {
  font-family: monospace;
  font-size: 0.8rem;
  color: #6b7280;
  white-space: nowrap;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-id {
  font-weight: 500;
  color: #111827;
  font-size: 0.875rem;
}

.target-user {
  font-size: 0.75rem;
  color: #6b7280;
}

.type-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background: #e5e7eb;
  color: #374151;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.severity-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.severity-info {
  background: #dbeafe;
  color: #1e40af;
}

.severity-warning {
  background: #fef3c7;
  color: #92400e;
}

.severity-error {
  background: #fee2e2;
  color: #dc2626;
}

.severity-critical {
  background: #fde2e8;
  color: #be185d;
}

.action-created {
  background: #d1fae5;
  color: #065f46;
}

.action-updated {
  background: #e0f2fe;
  color: #0369a1;
}

.action-deleted {
  background: #fee2e2;
  color: #dc2626;
}

.action-login {
  background: #e0e7ff;
  color: #4338ca;
}

.action-logout {
  background: #f3e8ff;
  color: #7c3aed;
}

.action-error {
  background: #fef3c7;
  color: #92400e;
}

.action-viewed {
  background: #f0f9ff;
  color: #0284c7;
}

.action-default {
  background: #f3f4f6;
  color: #374151;
}

.details-preview {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-item {
  font-size: 0.75rem;
  color: #6b7280;
}

.detail-item.error {
  color: #dc2626;
}

.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.pagination-button {
  padding: 0.5rem 1rem;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-button:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: #6b7280;
  font-size: 0.875rem;
}

.no-data {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
  font-style: italic;
}
